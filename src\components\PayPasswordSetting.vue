<template>
  <view class="pay-password-setting">
    <!-- 头部区域 -->
    <view class="header">
      <view class="back-btn" @click="handleBack">
        <uni-icons type="back" size="24" color="#333333"></uni-icons>
      </view>
      <view class="title">{{ currentStep === 1 ? '新支付密码' : '新支付密码-完成' }}</view>
      <view class="placeholder"></view>
    </view>
    
    <!-- 内容区域 -->
    <view class="content">
      <!-- 步骤1：设置密码 -->
      <view v-if="currentStep === 1" class="step-content">
        <view class="step-title">设置密码，用于支付验证</view>
        <view class="step-subtitle">不能是连续或密码或连续数字</view>
        
        <!-- 密码输入框 -->
        <view class="password-input">
          <view 
            class="input-box" 
            v-for="(item, index) in 6" 
            :key="index"
            :class="{ 'filled': index < firstPassword.length, 'error': hasError }"
          >
            <text v-if="index < firstPassword.length" class="password-dot">•</text>
          </view>
        </view>
        
        <!-- 错误提示 -->
        <view v-if="errorMessage" class="error-message">
          <text class="error-text">{{ errorMessage }}</text>
        </view>
      </view>
      
      <!-- 步骤2：确认密码 -->
      <view v-if="currentStep === 2" class="step-content">
        <view class="step-title">请再次输入，以确认密码</view>
        <view class="step-subtitle">不能是连续或密码或连续数字</view>
        
        <!-- 密码输入框 -->
        <view class="password-input">
          <view 
            class="input-box" 
            v-for="(item, index) in 6" 
            :key="index"
            :class="{ 'filled': index < secondPassword.length, 'error': hasError }"
          >
            <text v-if="index < secondPassword.length" class="password-dot">•</text>
          </view>
        </view>
        
        <!-- 显示输入的密码（用于确认） -->
        <view class="confirm-display">
          <view 
            class="confirm-box" 
            v-for="(digit, index) in firstPasswordArray" 
            :key="index"
          >
            <text class="confirm-digit">{{ digit }}</text>
          </view>
        </view>
        
        <!-- 错误提示 -->
        <view v-if="errorMessage" class="error-message">
          <text class="error-text">{{ errorMessage }}</text>
        </view>
        
        <!-- 完成按钮 -->
        <view v-if="secondPassword.length === 6" class="complete-section">
          <button 
            class="complete-btn" 
            :disabled="isSubmitting"
            @click="handleComplete"
          >
            {{ isSubmitting ? '密码设置中...' : '密码设置成功' }}
          </button>
        </view>
      </view>
    </view>
    
    <!-- 数字键盘 -->
    <view class="keyboard">
      <view class="keyboard-row" v-for="(row, rowIndex) in keyboardLayout" :key="rowIndex">
        <view 
          class="keyboard-key" 
          v-for="(key, keyIndex) in row" 
          :key="keyIndex"
          :class="{ 'key-delete': key === 'delete', 'key-disabled': key === '' || isSubmitting }"
          @click="handleKeyPress(key)"
        >
          <text v-if="key === 'delete'" class="delete-icon">⌫</text>
          <text v-else-if="key !== ''" class="key-text">{{ key }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'

// 组件属性定义
const props = defineProps({
  // 设置密码的API函数
  setPasswordFunction: {
    type: Function,
    default: null
  },
  // 是否是修改密码模式（需要先验证旧密码）
  isModifyMode: {
    type: Boolean,
    default: false
  }
})

// 组件事件定义
const emit = defineEmits([
  'success',    // 设置成功
  'failed',     // 设置失败
  'cancel',     // 取消操作
  'back'        // 返回上一步
])

// 响应式数据
const currentStep = ref(1)  // 当前步骤：1-设置密码，2-确认密码
const firstPassword = ref('')   // 第一次输入的密码
const secondPassword = ref('')  // 第二次输入的密码
const errorMessage = ref('')    // 错误信息
const hasError = ref(false)     // 是否有错误
const isSubmitting = ref(false) // 是否正在提交

// 数字键盘布局
const keyboardLayout = reactive([
  ['1', '2', '3'],
  ['4', '5', '6'], 
  ['7', '8', '9'],
  ['', '0', 'delete']
])

// 计算属性：将第一次密码转换为数组用于显示
const firstPasswordArray = computed(() => {
  return firstPassword.value.split('')
})

// 当前输入的密码
const currentPassword = computed(() => {
  return currentStep.value === 1 ? firstPassword.value : secondPassword.value
})

/**
 * 验证密码安全性
 * @param {string} password - 要验证的密码
 * @returns {object} 验证结果
 */
const validatePassword = (password) => {
  if (password.length !== 6) {
    return { valid: false, message: '密码必须为6位数字' }
  }
  
  // 检查是否为纯数字
  if (!/^\d{6}$/.test(password)) {
    return { valid: false, message: '密码只能包含数字' }
  }
  
  // 检查连续数字（如123456、654321）
  let hasConsecutive = false
  for (let i = 0; i < password.length - 2; i++) {
    const num1 = parseInt(password[i])
    const num2 = parseInt(password[i + 1])
    const num3 = parseInt(password[i + 2])
    
    // 检查递增连续
    if (num2 === num1 + 1 && num3 === num2 + 1) {
      hasConsecutive = true
      break
    }
    
    // 检查递减连续
    if (num2 === num1 - 1 && num3 === num2 - 1) {
      hasConsecutive = true
      break
    }
  }
  
  if (hasConsecutive) {
    return { valid: false, message: '不能包含连续的三个数字' }
  }
  
  // 检查重复数字（如111、222、333等）
  let hasRepeated = false
  for (let i = 0; i < password.length - 2; i++) {
    if (password[i] === password[i + 1] && password[i + 1] === password[i + 2]) {
      hasRepeated = true
      break
    }
  }
  
  if (hasRepeated) {
    return { valid: false, message: '不能包含重复的三个数字' }
  }
  
  return { valid: true, message: '' }
}

/**
 * 处理键盘按键点击
 * @param {string} key - 按键值
 */
const handleKeyPress = (key) => {
  if (isSubmitting.value) return
  
  // 清除错误状态
  if (hasError.value) {
    hasError.value = false
    errorMessage.value = ''
  }
  
  if (key === 'delete') {
    // 删除最后一位
    if (currentStep.value === 1) {
      if (firstPassword.value.length > 0) {
        firstPassword.value = firstPassword.value.slice(0, -1)
      }
    } else {
      if (secondPassword.value.length > 0) {
        secondPassword.value = secondPassword.value.slice(0, -1)
      }
    }
  } else if (key !== '' && currentPassword.value.length < 6) {
    // 添加数字
    if (currentStep.value === 1) {
      firstPassword.value += key
    } else {
      secondPassword.value += key
    }
  }
}

/**
 * 监听第一次密码输入完成
 */
watch(() => firstPassword.value, (newVal) => {
  if (newVal.length === 6) {
    // 验证密码安全性
    const validation = validatePassword(newVal)
    if (!validation.valid) {
      showError(validation.message)
      // 延迟清空密码
      setTimeout(() => {
        firstPassword.value = ''
      }, 1000)
    } else {
      // 密码有效，进入下一步
      setTimeout(() => {
        currentStep.value = 2
      }, 500)
    }
  }
})

/**
 * 监听第二次密码输入完成
 */
watch(() => secondPassword.value, (newVal) => {
  if (newVal.length === 6) {
    // 检查两次密码是否一致
    if (newVal !== firstPassword.value) {
      showError('两次输入的密码不一致')
      setTimeout(() => {
        secondPassword.value = ''
      }, 1000)
    }
  }
})

/**
 * 显示错误信息
 * @param {string} message - 错误信息
 */
const showError = (message) => {
  errorMessage.value = message
  hasError.value = true
  
  // 震动反馈
  uni.vibrateShort()
}

/**
 * 完成密码设置
 */
const handleComplete = async () => {
  if (secondPassword.value !== firstPassword.value) {
    showError('两次输入的密码不一致')
    return
  }
  
  try {
    isSubmitting.value = true
    
    // 如果提供了设置密码函数，则调用
    if (props.setPasswordFunction && typeof props.setPasswordFunction === 'function') {
      const result = await props.setPasswordFunction(firstPassword.value)
      if (result) {
        handleSuccess()
      } else {
        handleFailed('密码设置失败，请重试')
      }
    } else {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000))
      handleSuccess()
    }
    
  } catch (error) {
    console.error('设置支付密码失败:', error)
    handleFailed('密码设置失败，请重试')
  } finally {
    isSubmitting.value = false
  }
}

/**
 * 设置成功处理
 */
const handleSuccess = () => {
  emit('success', firstPassword.value)
}

/**
 * 设置失败处理
 * @param {string} message - 错误信息
 */
const handleFailed = (message) => {
  showError(message)
  emit('failed', message)
}

/**
 * 返回处理
 */
const handleBack = () => {
  if (currentStep.value === 2) {
    // 返回第一步
    currentStep.value = 1
    secondPassword.value = ''
    errorMessage.value = ''
    hasError.value = false
  } else {
    // 退出组件
    emit('back')
    emit('cancel')
  }
}

/**
 * 重置组件状态
 */
const reset = () => {
  currentStep.value = 1
  firstPassword.value = ''
  secondPassword.value = ''
  errorMessage.value = ''
  hasError.value = false
  isSubmitting.value = false
}

// 暴露方法给父组件
defineExpose({
  reset
})
</script>

<style lang="scss" scoped>
.pay-password-setting {
  height: 100vh;
  background-color: #f7f7f7;
  display: flex;
  flex-direction: column;
}

/* 头部区域 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;

  .back-btn {
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333333;
  }

  .placeholder {
    width: 48rpx;
    height: 48rpx;
  }
}

/* 内容区域 */
.content {
  flex: 1;
  padding: 80rpx 32rpx 40rpx;

  .step-content {
    .step-title {
      font-size: 32rpx;
      color: #333333;
      margin-bottom: 16rpx;
      text-align: center;
    }

    .step-subtitle {
      font-size: 28rpx;
      color: #999999;
      margin-bottom: 80rpx;
      text-align: center;
    }
  }
}

/* 密码输入框 */
.password-input {
  display: flex;
  justify-content: center;
  gap: 24rpx;
  margin-bottom: 40rpx;

  .input-box {
    width: 80rpx;
    height: 80rpx;
    border: 2rpx solid #e0e0e0;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #ffffff;
    transition: all 0.3s ease;

    &.filled {
      border-color: #4CAF50;
      background-color: #f0f8ff;
    }

    &.error {
      border-color: #ff4757;
      background-color: #fff5f5;
      animation: shake 0.5s ease-in-out;
    }

    .password-dot {
      font-size: 48rpx;
      color: #333333;
      font-weight: bold;
    }
  }
}

/* 确认密码显示 */
.confirm-display {
  display: flex;
  justify-content: center;
  gap: 24rpx;
  margin: 60rpx 0 40rpx;

  .confirm-box {
    width: 80rpx;
    height: 80rpx;
    border: 2rpx solid #4CAF50;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e8f5e8;

    .confirm-digit {
      font-size: 36rpx;
      color: #4CAF50;
      font-weight: 600;
    }
  }
}

/* 错误提示 */
.error-message {
  text-align: center;
  margin-bottom: 32rpx;

  .error-text {
    font-size: 28rpx;
    color: #ff4757;
  }
}

/* 完成按钮 */
.complete-section {
  margin-top: 60rpx;

  .complete-btn {
    width: 400rpx;
    height: 80rpx;
    background-color: #333333;
    color: #ffffff;
    font-size: 28rpx;
    border-radius: 40rpx;
    border: none;
    margin: 0 auto;
    display: block;

    &:disabled {
      background-color: #cccccc;
    }

    &:active:not(:disabled) {
      background-color: #555555;
    }
  }
}

/* 数字键盘 */
.keyboard {
  background-color: #ffffff;
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;

  .keyboard-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .keyboard-key {
    width: 200rpx;
    height: 100rpx;
    background-color: #f8f9fa;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:active:not(.key-disabled) {
      background-color: #e9ecef;
      transform: scale(0.95);
    }

    &.key-delete {
      background-color: #f1f3f4;

      .delete-icon {
        font-size: 36rpx;
        color: #666666;
      }
    }

    &.key-disabled {
      background-color: transparent;
      opacity: 0.6;
    }

    .key-text {
      font-size: 48rpx;
      font-weight: 500;
      color: #333333;
    }
  }
}

/* 震动动画 */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-10rpx); }
  20%, 40%, 60%, 80% { transform: translateX(10rpx); }
}

/* 提交中状态 */
.submitting {
  .keyboard-key {
    opacity: 0.6;
    pointer-events: none;
  }
}
</style>
