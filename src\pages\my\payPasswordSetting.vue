<template>
  <PayPasswordSetting 
    :set-password-function="handleSetPassword"
    :is-modify-mode="isModifyMode"
    @success="onSetPasswordSuccess"
    @failed="onSetPasswordFailed"
    @cancel="onSetPasswordCancel"
    @back="onSetPasswordBack"
  />
</template>

<script setup>
import { ref, onMounted } from 'vue'
import PayPasswordSetting from '@/components/PayPasswordSetting.vue'
// import { setPayPasswordAPI } from '@/api/index.js'

// 响应式数据
const isModifyMode = ref(false)

/**
 * 处理设置支付密码
 * @param {string} password - 新密码
 * @returns {Promise<boolean>} 设置结果
 */
const handleSetPassword = async (password) => {
  try {
    // 可以选择使用真实API或模拟数据
    // const result = await setPayPasswordAPI(password)
    // return result
    
    // 当前使用模拟API，实际开发时取消上面注释并删除下面的模拟逻辑
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟90%成功率
    const success = Math.random() > 0.1
    
    if (success) {
      console.log('支付密码设置成功:', password)
      return true
    } else {
      console.log('支付密码设置失败')
      return false
    }
    
  } catch (error) {
    console.error('设置支付密码失败:', error)
    return false
  }
}

/**
 * 设置成功回调
 * @param {string} password - 设置的密码
 */
const onSetPasswordSuccess = (password) => {
  console.log('支付密码设置成功:', password)
  
  uni.showToast({
    title: '支付密码设置成功',
    icon: 'success',
    duration: 2000
  })
  
  // 延迟返回上一页
  setTimeout(() => {
    uni.navigateBack()
  }, 2000)
}

/**
 * 设置失败回调
 * @param {string} errorMessage - 错误信息
 */
const onSetPasswordFailed = (errorMessage) => {
  console.log('支付密码设置失败:', errorMessage)
  
  uni.showToast({
    title: errorMessage,
    icon: 'none',
    duration: 2000
  })
}

/**
 * 取消设置回调
 */
const onSetPasswordCancel = () => {
  console.log('用户取消设置支付密码')
  uni.navigateBack()
}

/**
 * 返回回调
 */
const onSetPasswordBack = () => {
  console.log('用户点击返回')
  uni.navigateBack()
}

// 页面挂载时获取参数
onMounted(() => {
  // 获取页面参数，判断是新设置还是修改密码
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}
  
  // 如果传入了 modify=true 参数，则为修改模式
  isModifyMode.value = options.modify === 'true'
  
  console.log('支付密码设置页面已挂载，模式:', isModifyMode.value ? '修改密码' : '新设置密码')
})
</script>

<style lang="scss" scoped>
/* 这个页面直接使用组件，不需要额外样式 */
</style>
