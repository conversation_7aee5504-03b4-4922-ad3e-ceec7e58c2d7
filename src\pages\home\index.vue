<template>
  <view class="home">
    <view class="bg"></view>
    <view class="body">
      <view class="list">
        <template v-if="state.mode == 1">
          <view class="item item-0" @click="toStartPage">
            <image src="@/static/bg/home-menu-0.png" class="img img-0"></image>
            <view class="title">我要报名</view>
          </view>
          <view class="item item-1" @click="tolookInfoPage">
            <image src="@/static/bg/home-menu-1.png" class="img img-1"></image>
            <view class="title">查看报名信息</view>
          </view>
          <view class="item item-2" @click="lookPage">
            <image src="@/static/bg/home-menu-2.png" class="img img-2"></image>
            <view class="title">查看预录取通知书</view>
          </view>
        </template>
        <template v-else-if="state.mode == 2">
          <view class="item item-3" @click="toCheckInPage">
            <image src="@/static/bg/home-menu-0.png" class="img img-3"></image>
            <view class="title">我要报到</view>
          </view>
          <view class="item item-4" @click="toLookCheckInPage">
            <image src="@/static/bg/home-menu-1.png" class="img img-4"></image>
            <view class="title">查询报到信息</view>
          </view>
          <view class="item item-5" @click="toPaymentPage">
            <image src="@/static/bg/home-menu-5.png" class="img img-5"></image>
            <view class="title">我要缴费</view>
          </view>
        </template>
        <view v-else> </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { onMounted, reactive } from "vue";
import http from "@/utils/request";
import { onLoad } from "@dcloudio/uni-app";

const state = reactive({
  mode: null, //1 报名 2 报到
});

// 开始报名
const toStartPage = () => {
  http
    .post("/app/enrollment/intention/checkSignUp", {
      id: uni.getStorageSync("planId") || null,
    })
    .then((res) => {
      uni.setStorageSync("planName", res.data.planName);
      uni.setStorageSync("schoolName", res.data.schoolName);
      uni.navigateTo({
        url: `/pages/signUp/start`,
      });
    });
};

const tolookInfoPage = () => {
  uni.navigateTo({
    url: `/pages/signUp/lookSignInfo`,
  });
};

const toPaymentPage = () => {
  uni.navigateTo({
    url: `/pages/checkIn/startPay`,
  });
};

const lookPage = () => {
  uni.navigateTo({
    url: `/pages/signUp/admission`,
  });
};

const toLookCheckInPage = () => {
  uni.navigateTo({
    url: `/pages/checkIn/queryCheckInInfo`,
  });
};

// 扫码进入首页会先发请求校验url携带的id 获取到一些信息存到浏览器缓存吧
// const getCheckBarCode = () => {
//   http
//     .post("/app/enrollment/intention/checkSignUp", {
//       id: "1932996792615665665",
//     })
//     .then((res) => {
//       console.log(res);
//     });
// };

const toCheckInPage = () => {
  // 调用我要报到的接口获取数据 成功获取到数据后跳转到短信验证码的界面
  http
    .post("/app/enrollment/report/checkReport", {
      id: uni.getStorageSync("planId") || null,
    })
    .then((res) => {
      uni.setStorageSync("planName", res.data.planName);
      uni.setStorageSync("schoolName", res.data.schoolName);
      uni.navigateTo({
        url: `/pages/checkIn/start`,
      });
    });
};

onMounted(() => {
  state.mode = uni.getStorageSync("mode") || null;
});

onLoad((options) => {
  if (options.mode) {
    state.mode = options.mode;
    uni.setStorageSync("mode", options.mode);
  }
  if (options.planId) {
    uni.setStorageSync("planId", options.planId);
  }
  if (options.intentionTypeId) {
    uni.setStorageSync("intentionTypeId", options.intentionTypeId);
  }
});
</script>

<style lang="scss" scoped>
.home {
  position: relative;
  min-height: 100vh;
  background-color: #00cf78;
  display: flex;
  flex-direction: column;
  .bg {
    background: url("@/static/bg/home-bg.png") no-repeat;
    width: 100%;
    height: 602rpx;
    // padding-top: 80%;
    // margin-top: -45px;
    background-size: 100% auto;
    // max-height: 80%;
  }
  .body {
    background-color: #fff;
    flex: 1;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      left: 0;
      right: 0;
      top: -28px;
      height: 30px;
      background-color: #fff;
      border-radius: 28px 28px 0px 0px;
      z-index: 1;
    }

    .list {
      height: 100%;
      padding: 104rpx 36rpx 104rpx 36rpx;
      .item {
        background-color: #fdf4e4;
        border-radius: 16rpx;
        margin-bottom: 34rpx;
        display: flex;
        align-items: center;
        &:active {
          background-color: #eee;
        }
        &:last-child {
          margin-bottom: 0;
        }
        .title {
          color: #ff8f00;
          font-weight: 500;
          font-size: 36rpx;
        }
        .img-0 {
          width: 132rpx;
          height: 132rpx;
          margin-right: 42rpx;
        }
        .img-1 {
          width: 140rpx;
          height: 140rpx;
          margin-right: 36rpx;
        }
        .img-2 {
          width: 132rpx;
          height: 132rpx;
          margin-right: 48rpx;
        }
        .img-3 {
          width: 132rpx;
          height: 132rpx;
          margin-right: 42rpx;
        }
        .img-4 {
          width: 140rpx;
          height: 140rpx;
          margin-right: 36rpx;
        }
        .img-5 {
          width: 128rpx;
          height: 128rpx;
          margin-right: 50rpx;
        }
      }

      .item-0 {
        padding: 48rpx 48rpx 26rpx 48rpx;
      }
      .item-1 {
        padding: 30rpx 46rpx 36rpx 46rpx;
        background-color: #fff9e3;
        .title {
          color: #ffa300;
        }
      }
      .item-2 {
        padding: 40rpx 42rpx 34rpx 42rpx;
        background-color: #e9faf5;
        .title {
          color: #00b781;
        }
      }
      .item-3 {
        padding: 48rpx 48rpx 26rpx 48rpx;
      }
      .item-4 {
        padding: 30rpx 46rpx 36rpx 46rpx;
        background-color: #fff9e3;
        .title {
          color: #ffa300;
        }
      }
      .item-5 {
        padding: 42rpx 44rpx 36rpx 44rpx;
        background-color: #fff5f5;
        .title {
          color: #ff7373;
        }
      }
    }
  }
}
</style>
