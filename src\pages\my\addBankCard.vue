<template>
  <view class="add-bank-card-page">
    <!-- 头部导航 -->
    <view class="header">
      <view class="back-btn" @click="handleBack">
        <uni-icons type="back" size="24" color="#333333"></uni-icons>
      </view>
      <view class="title">{{ pageTitle }}</view>
      <view class="placeholder"></view>
    </view>
    
    <!-- 步骤1：首次绑定 - 基本信息和设置支付密码 -->
    <view v-if="currentStep === 1" class="step-content">
      <view class="form-section">
        <!-- 姓名 -->
        <view class="form-item">
          <text class="form-label">姓名</text>
          <uni-easyinput 
            v-model="userInfo.name" 
            placeholder="请输入姓名"
            :clearable="false"
          />
        </view>
        
        <!-- 手机号 -->
        <view class="form-item">
          <text class="form-label">手机号</text>
          <uni-easyinput 
            v-model="userInfo.phone" 
            placeholder="请输入手机号"
            type="number"
            maxlength="11"
            :clearable="false"
          />
        </view>
        
        <!-- 设置支付密码 -->
        <view class="form-item">
          <text class="form-label">*设置支付密码</text>
          <view class="password-setting-row" @click="handleSetPayPassword">
            <view class="password-display">
              <text v-if="!hasPayPassword" class="password-placeholder">请设置支付密码</text>
              <view v-else class="password-dots">
                <text class="dot" v-for="n in 6" :key="n">*</text>
              </view>
            </view>
            <uni-icons type="right" size="16" color="#999999"></uni-icons>
          </view>
        </view>
      </view>
      
      <!-- 下一步按钮 -->
      <view class="bottom-section">
        <button 
          class="next-btn" 
          :class="{ 'disabled': !canGoNext }"
          :disabled="!canGoNext"
          @click="handleNext"
        >
          下一步
        </button>
      </view>
    </view>
    
    <!-- 步骤2：银行卡信息表单 -->
    <view v-if="currentStep === 2" class="step-content">
      <view class="form-section">
        <!-- 姓名 -->
        <view class="form-item">
          <text class="form-label">*姓名</text>
          <uni-easyinput 
            v-model="bankCardInfo.name" 
            placeholder="请输入持卡人姓名"
            :clearable="false"
          />
        </view>
        
        <!-- 身份证号 -->
        <view class="form-item">
          <text class="form-label">*身份证号码</text>
          <uni-easyinput 
            v-model="bankCardInfo.idCard" 
            placeholder="请输入身份证号码"
            maxlength="18"
            :clearable="false"
          />
        </view>
        
        <!-- 开户银行 -->
        <view class="form-item">
          <text class="form-label">*开户银行</text>
          <uni-data-select
            v-model="bankCardInfo.bankCode"
            :localdata="bankOptions"
            placeholder="请选择开户银行"
          />
        </view>
        
        <!-- 银行卡类型 -->
        <view class="form-item">
          <text class="form-label">*银行卡类型</text>
          <uni-data-select
            v-model="bankCardInfo.cardType"
            :localdata="cardTypeOptions"
            placeholder="请选择卡类型"
          />
        </view>
        
        <!-- 银行卡号 -->
        <view class="form-item">
          <text class="form-label">*银行卡号</text>
          <uni-easyinput 
            v-model="bankCardInfo.cardNumber" 
            placeholder="请输入银行卡号"
            type="number"
            maxlength="19"
            :clearable="false"
          />
        </view>
        
        <!-- 手机号 -->
        <view class="form-item">
          <text class="form-label">*手机号码</text>
          <uni-easyinput 
            v-model="bankCardInfo.phone" 
            placeholder="请输入手机号码"
            type="number"
            maxlength="11"
            :clearable="false"
          />
        </view>
        
        <!-- 短信验证码 -->
        <view class="form-item">
          <text class="form-label">*短信验证码</text>
          <view class="sms-input-row">
            <uni-easyinput 
              v-model="bankCardInfo.smsCode" 
              placeholder="请输入短信验证码"
              type="number"
              maxlength="6"
              :clearable="false"
              class="sms-input"
            />
            <button 
              class="sms-btn" 
              :class="{ 'disabled': !canSendSms || smsCountdown > 0 }"
              :disabled="!canSendSms || smsCountdown > 0"
              @click="handleSendSms"
            >
              {{ smsCountdown > 0 ? `${smsCountdown}s` : '发送' }}
            </button>
          </view>
        </view>
      </view>
      
      <!-- 协议和确定按钮 -->
      <view class="bottom-section">
        <!-- 协议勾选 -->
        <view class="agreement-section">
          <uni-data-checkbox 
            v-model="agreementChecked" 
            :localdata="agreementOptions"
            mode="tag"
            @change="handleAgreementChange"
          />
          <view class="agreement-text">
            <text>我已阅读并同意</text>
            <text class="agreement-link" @click="showAgreement">《银行卡绑定协议》</text>
          </view>
        </view>
        
        <!-- 确定按钮 -->
        <button 
          class="confirm-btn" 
          :class="{ 'disabled': !canSubmit }"
          :disabled="!canSubmit"
          @click="handleSubmit"
        >
          确定
        </button>
      </view>
    </view>
    

  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'

// 响应式数据
const currentStep = ref(1)  // 当前步骤：1-基本信息，2-银行卡信息
const isFirstBinding = ref(true)  // 是否首次绑定
const hasPayPassword = ref(false)  // 是否已设置支付密码
const smsCountdown = ref(0)  // 短信倒计时
const agreementChecked = ref([])  // 协议勾选状态

// 用户基本信息
const userInfo = reactive({
  name: '',
  phone: ''
})

// 银行卡信息
const bankCardInfo = reactive({
  name: '',
  idCard: '',
  bankCode: '',
  cardType: '',
  cardNumber: '',
  phone: '',
  smsCode: ''
})

// 银行选项
const bankOptions = reactive([
  { value: 'ICBC', text: '中国工商银行' },
  { value: 'ABC', text: '中国农业银行' },
  { value: 'BOC', text: '中国银行' },
  { value: 'CCB', text: '中国建设银行' },
  { value: 'COMM', text: '交通银行' },
  { value: 'CMB', text: '招商银行' },
  { value: 'CITIC', text: '中信银行' },
  { value: 'CEB', text: '光大银行' },
  { value: 'CMBC', text: '中国民生银行' },
  { value: 'PAB', text: '平安银行' }
])

// 银行卡类型选项
const cardTypeOptions = reactive([
  { value: 'debit', text: '储蓄卡' },
  { value: 'credit', text: '信用卡' }
])

// 协议选项
const agreementOptions = reactive([
  { value: 'agree', text: '' }
])

// 计算属性
const pageTitle = computed(() => {
  if (currentStep.value === 1) {
    return isFirstBinding.value ? '添加银行卡-首次绑定' : '添加银行卡'
  } else {
    return '添加银行卡-再次绑定'
  }
})

const canGoNext = computed(() => {
  return userInfo.name.trim() && 
         userInfo.phone.trim() && 
         /^1[3-9]\d{9}$/.test(userInfo.phone) &&
         hasPayPassword.value
})

const canSendSms = computed(() => {
  return bankCardInfo.phone.trim() && /^1[3-9]\d{9}$/.test(bankCardInfo.phone)
})

const canSubmit = computed(() => {
  return bankCardInfo.name.trim() &&
         bankCardInfo.idCard.trim() &&
         bankCardInfo.bankCode &&
         bankCardInfo.cardType &&
         bankCardInfo.cardNumber.trim() &&
         bankCardInfo.phone.trim() &&
         bankCardInfo.smsCode.trim() &&
         agreementChecked.value.includes('agree')
})

/**
 * 检查是否已绑定银行卡
 */
const checkExistingBankCards = async () => {
  try {
    // 获取页面参数
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const options = currentPage.options || {}

    // 如果传入了 existing=true 参数，则模拟已有银行卡用户
    if (options.existing === 'true') {
      isFirstBinding.value = false
      currentStep.value = 2
      hasPayPassword.value = true
      return
    }

    // 模拟API调用检查是否已有银行卡
    // const result = await getBankCardList()

    // 模拟：检查用户是否已有银行卡
    const hasExistingCards = false  // 这里应该根据实际API结果判断

    if (hasExistingCards) {
      isFirstBinding.value = false
      currentStep.value = 2
      // 如果已有银行卡，说明已设置过支付密码
      hasPayPassword.value = true
    }

  } catch (error) {
    console.error('检查银行卡失败:', error)
  }
}

/**
 * 设置支付密码
 */
const handleSetPayPassword = () => {
  // 跳转到设置支付密码页面
  uni.navigateTo({
    url: '/pages/my/payPasswordSetting?from=addBankCard',
    success: () => {
      console.log('跳转到设置支付密码页面')
    },
    fail: (error) => {
      console.error('跳转失败:', error)
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none'
      })
    }
  })
}

/**
 * 下一步
 */
const handleNext = () => {
  if (!canGoNext.value) return
  
  // 将基本信息复制到银行卡信息
  bankCardInfo.name = userInfo.name
  bankCardInfo.phone = userInfo.phone
  
  currentStep.value = 2
}

/**
 * 发送短信验证码
 */
const handleSendSms = async () => {
  if (!canSendSms.value || smsCountdown.value > 0) return
  
  try {
    // 模拟发送短信API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    uni.showToast({
      title: '验证码已发送',
      icon: 'success'
    })
    
    // 开始倒计时
    smsCountdown.value = 60
    const timer = setInterval(() => {
      smsCountdown.value--
      if (smsCountdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
    
  } catch (error) {
    console.error('发送短信失败:', error)
    uni.showToast({
      title: '发送失败，请重试',
      icon: 'none'
    })
  }
}

/**
 * 协议勾选变化
 */
const handleAgreementChange = (e) => {
  console.log('协议勾选状态:', e)
}

/**
 * 显示协议
 */
const showAgreement = () => {
  uni.showModal({
    title: '银行卡绑定协议',
    content: '这里是银行卡绑定协议的详细内容...',
    showCancel: false
  })
}

/**
 * 提交银行卡信息
 */
const handleSubmit = async () => {
  if (!canSubmit.value) return
  
  try {
    uni.showLoading({
      title: '绑定中...'
    })
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    uni.hideLoading()
    uni.showToast({
      title: '银行卡绑定成功',
      icon: 'success'
    })
    
    // 延迟返回
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
    
  } catch (error) {
    console.error('绑定银行卡失败:', error)
    uni.hideLoading()
    uni.showToast({
      title: '绑定失败，请重试',
      icon: 'none'
    })
  }
}

/**
 * 返回处理
 */
const handleBack = () => {
  if (currentStep.value === 2 && isFirstBinding.value) {
    // 从步骤2返回步骤1
    currentStep.value = 1
  } else {
    // 退出页面
    uni.navigateBack()
  }
}

/**
 * 支付密码设置成功回调
 */
const onPayPasswordSetSuccess = () => {
  hasPayPassword.value = true
  console.log('支付密码设置成功，更新状态')
}

// 页面挂载时检查
onMounted(() => {
  checkExistingBankCards()

  // 监听支付密码设置成功事件
  uni.$on('payPasswordSetSuccess', onPayPasswordSetSuccess)
})

// 页面卸载时移除事件监听
onUnmounted(() => {
  uni.$off('payPasswordSetSuccess', onPayPasswordSetSuccess)
})
</script>

<style lang="scss" scoped>
.add-bank-card-page {
  min-height: 100vh;
  background-color: #f7f7f7;
  display: flex;
  flex-direction: column;
}

/* 头部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;

  .back-btn {
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333333;
  }

  .placeholder {
    width: 48rpx;
    height: 48rpx;
  }
}

/* 步骤内容 */
.step-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 表单区域 */
.form-section {
  flex: 1;
  padding: 32rpx;

  .form-item {
    margin-bottom: 40rpx;

    .form-label {
      display: block;
      font-size: 28rpx;
      color: #333333;
      margin-bottom: 16rpx;

      &::before {
        content: '*';
        color: #ff4757;
        margin-right: 4rpx;
      }
    }

    /* 支付密码设置行 */
    .password-setting-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx 32rpx;
      background-color: #ffffff;
      border-radius: 12rpx;
      border: 2rpx solid #e0e0e0;

      .password-display {
        flex: 1;

        .password-placeholder {
          font-size: 28rpx;
          color: #999999;
        }

        .password-dots {
          display: flex;
          gap: 8rpx;

          .dot {
            font-size: 32rpx;
            color: #333333;
            font-weight: bold;
          }
        }
      }
    }

    /* 短信验证码输入行 */
    .sms-input-row {
      display: flex;
      gap: 16rpx;

      .sms-input {
        flex: 1;
      }

      .sms-btn {
        width: 160rpx;
        height: 80rpx;
        background-color: #007AFF;
        color: #ffffff;
        font-size: 28rpx;
        border-radius: 12rpx;
        border: none;

        &.disabled {
          background-color: #cccccc;
          color: #999999;
        }

        &:active:not(.disabled) {
          background-color: #0056b3;
        }
      }
    }
  }
}

/* 底部区域 */
.bottom-section {
  padding: 32rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;

  /* 协议区域 */
  .agreement-section {
    display: flex;
    align-items: center;
    margin-bottom: 32rpx;

    .agreement-text {
      margin-left: 16rpx;
      font-size: 26rpx;
      color: #666666;

      .agreement-link {
        color: #007AFF;
        text-decoration: underline;
      }
    }
  }

  /* 按钮样式 */
  .next-btn,
  .confirm-btn {
    width: 100%;
    height: 88rpx;
    background-color: #4CAF50;
    color: #ffffff;
    font-size: 32rpx;
    font-weight: 600;
    border-radius: 44rpx;
    border: none;

    &.disabled {
      background-color: #cccccc;
      color: #999999;
    }

    &:active:not(.disabled) {
      background-color: #45a049;
    }
  }
}

/* uni-ui组件样式调整 */
:deep(.uni-easyinput__content) {
  background-color: #ffffff;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 24rpx 32rpx;
}

:deep(.uni-easyinput__content-input) {
  font-size: 28rpx;
  color: #333333;
}

:deep(.uni-easyinput__placeholder-class) {
  color: #999999;
  font-size: 28rpx;
}

:deep(.uni-data-select) {
  background-color: #ffffff;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
}

:deep(.uni-select__input-text) {
  font-size: 28rpx;
  color: #333333;
  padding: 24rpx 32rpx;
}

:deep(.uni-select__input-placeholder) {
  color: #999999;
  font-size: 28rpx;
}

:deep(.uni-data-checklist) {
  .checklist-group {
    .checklist-box {
      margin: 0;
      padding: 0;

      .checkbox__inner {
        width: 32rpx;
        height: 32rpx;
        border-radius: 6rpx;
        border: 2rpx solid #e0e0e0;

        &.checkbox__inner--checked {
          background-color: #007AFF;
          border-color: #007AFF;
        }
      }
    }
  }
}
</style>
