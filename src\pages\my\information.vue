<template>
  <view class="informationPage">
    <view class="bgLine"></view>
    <view class="listPage">
      <view class="listItem">
        <view class="listItem_name">
          <view class="item_title">姓名：</view>
          <view>{{ "哦i见佛" }}</view>
        </view>
      </view>
      <view class="listItem">
        <view class="listItem_name">
          <view class="item_title">性别：</view>
          <view>{{ "男" }}</view>
        </view>
      </view>
      <view class="listItem">
        <view class="listItem_name">
          <view class="item_title">手机号码：</view>
          <view>{{ "110" }}</view>
        </view>
      </view>
      <view class="listItem">
        <view class="listItem_name">
          <view class="item_title"> 报到专业：</view>
          <view>{{ "打金" }}</view>
        </view>
      </view>
      <view class="listItem">
        <view class="listItem_name">
          <view class="item_title"> 班级名称：</view>
          <view>{{ "哦上帝哈佛" }}</view>
        </view>
      </view>
      <view class="listItem">
        <view class="listItem_name">
          <view class="item_title"> 宿舍号：</view>
          <view>{{ "402" }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup></script>

<style scoped>
.informationPage {
  min-height: 100vh;
  background-color: #f6f6f6;
}

.listPage {
  background: #ffffff;
  padding-left: 30rpx;
  padding-right: 30rpx;
}
.listItem {
  padding: 30rpx 0rpx 30rpx 0rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #e8e8e8;
}

.listItem_name {
  font-weight: 400;
  font-size: 24rpx;
  color: #333333;
  display: flex;
  align-items: center;
}

.item_title {
  font-weight: 400;
  font-size: 24rpx;
  color: #333333;
  padding-left: 16rpx;
  width: 140rpx;
}

.bgLine {
  height: 20rpx;
  background: #f6f6f6;
}
</style>
