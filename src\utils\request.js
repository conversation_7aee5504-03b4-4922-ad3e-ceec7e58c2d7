import { getToken, toLogin } from "@/utils/auth";
import { removeUrlDouble } from "@/utils/index";

let time = null;
const errorHandle = (code, message) => {
  uni.hideLoading();
  clearTimeout(time);
  time = setTimeout(() => {
    switch (code) {
      case 400:
        uni.showToast({
          title: message,
          icon: "error",
          duration: 3000,
        });
        break;
      case 401:
        uni.showToast({
          title: message,
          icon: "none",
          duration: 3000,
          success() {
            setTimeout(() => {
              toLogin();
            }, 2000);
          },
        });
        break;
      case 403:
        uni.showToast({
          title: message || "权限不足",
          icon: "none",
          duration: 3000,
        });
        break;
      case 404:
        uni.showToast({
          title: `资源不存在：${message}`,
          icon: "none",
          duration: 3000,
        });
        break;
      case 500:
        if (message.length > 40) {
          message = "请联系管理员！";
        }
        uni.showToast({
          title: `系统异常：${message || "请联系管理员！"}`,
          icon: "none",
          duration: 3000,
        });
        break;
      case 503:
        uni.showToast({
          title: `系统开小差：${message || "请稍后再试！"}`,
          icon: "none",
          duration: 3000,
        });
        break;

      case 1020004005:
        uni.showModal({
          title: "提示",
          content: "检测到您报名已完成，请勿重复报名。",
          icon: "none",
          showCancel: false,
          confirmColor: "#00B781",
          duration: 3000,
        });
        break;
      default:
        uni.showToast({
          title: message,
          icon: "none",
          duration: 3000,
        });

        break;
    }
  }, 500);
};

const defaultConfig = {
  timeout: 6000,
  withCredentials: true,
  header: {
    "Content-Type": "application/json",
  },
};
/**
 * @description: 网络请求
 * @param {Object} options
 * @return {Promise}
 */
const http = (options = {}) => {
  return uni.request({ ...defaultConfig, ...options });
};

http.get = (url, options, params = {}) => {
  return uni.request({
    ...defaultConfig,
    url,
    method: "GET",
    data: options,
    ...params,
  });
};
http.post = (url, options = {}, params = {}) => {
  const config = {
    ...defaultConfig,
    url,
    method: "POST",
    data: options,
    ...params,
  };
  return uni.request(config);
};

http.put = (url, options = {}, params = {}) => {
  const config = {
    ...defaultConfig,
    url,
    method: "PUT",
    data: options,
    ...params,
  };
  return uni.request(config);
};

http.delete = (url, options = {}, params = {}) => {
  const config = {
    ...defaultConfig,
    url,
    method: "DELETE",
    data: options,
    ...params,
  };
  return uni.request(config);
};

http.uploadFile = (filePath, folderType) => {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: import.meta.env.VITE_BASE_URL + "/file/common/upload",
      filePath: filePath,
      formData: { folderType },
      name: "file",
      success: (res) => {
        console.log(res);
        const r = JSON.parse(res.data);
        if (r.code == 0) {
          resolve(r.data[0]);
        } else {
          reject(r.data);
        }
      },
      fail: (error) => {
        console.log(error);

        reject(error);
      },
    });
  });
};

http.downloadFile = (url, options) => {
  return uni.downloadFile({ ...defaultConfig, url, ...options });
};

// 拦截器
const methodsList = ["request"];
const interceptor = {
  invoke(args) {
    const url = `${import.meta.env.VITE_BASE_URL}${args.url}`
    args.url = removeUrlDouble(url);
    const token = getToken();
    if (token) {
      if (args.header == undefined) args.header = {};
      args.header["Client-Authorization"] = `${token}`; //Bearer
    }
    if (args.header == undefined) {
      args.header = {};
    }
    args.header["Lang"] =
      window.localStorage.getItem("language") == "zh-Hans" ? "zh" : "en";
  },
  fail(err) {
    const { errMsg } = err;
    if (errMsg == "request:fail timeout") {
      uni.showToast({
        title: `请求超时，请稍后再试！`,
        icon: "none",
      });
    }
  },
  returnValue(ret) {
    return new Promise((resolve, reject) => {
      ret
        .then((response) => {
          console.log(response, "response");

          const { statusCode, data } = response;

          // 处理 HTTP 状态码
          if (statusCode !== 200) {
            errorHandle(statusCode, data?.message || data?.error || data?.msg);
            return reject();
          }

          // 处理字符串类型的响应数据
          let parsedData = data;
          if (typeof parsedData === "string") {
            try {
              parsedData = JSON.parse(parsedData);
            } catch (e) {
              errorHandle(
                parsedData.code,
                parsedData?.message || parsedData?.error || parsedData?.msg
              );
              return reject();
            }
          }

          // 处理业务状态码
          if (parsedData.code !== 0) {
            errorHandle(
              parsedData.code,
              parsedData?.message || parsedData?.error || parsedData?.msg
            );
            reject(parsedData);
          } else {
            resolve(parsedData); // 直接返回 data 字段
          }
        })
        .catch((err) => {
          // 处理网络错误
          let errorMessage = "接口发送失败，请求超时，请稍后再试";

          reject(new Error(errorMessage));
        });
    });
  },
};

methodsList.forEach((method) => {
  uni.addInterceptor(method, interceptor);
});

export default http;
