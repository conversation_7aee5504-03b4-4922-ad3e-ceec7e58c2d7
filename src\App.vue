<template>
  <view>
    <!-- 微信浏览器检测组件 -->
    <WechatBrowserCheck />
  </view>
</template>

<script setup>
import { ref, onMounted } from "vue";
import WechatBrowserCheck from "@/components/WechatBrowserCheck.vue";
import { onLaunch }from'@dcloudio/uni-app'

// #ifdef H5
import VConsole from "vconsole";
// #endif

// 路由拦截器设置
["redirectTo", "navigateTo"].forEach((method) => {
  uni.addInterceptor(method, {
    success(args) {
      // console.log('进入页面', args)
    },
  });
});

// 生命周期钩子
onMounted(() => {
  // #ifdef H5
  document.addEventListener(
    "touchstart",
    (event) => {
      if (event.touches.length > 1) {
        event.preventDefault();
      }
    },
    { passive: false }
  );

  document.addEventListener("gesturestart", (event) => {
    event.preventDefault();
  });
  // #endif
});

onLaunch(() => {
    //  const vConsole = new VConsole();
  // const isRun = ["development"].includes(import.meta.env.MODE);
  // if (isRun) {
  //   // eslint-disable-next-line no-unused-vars
  //   const vConsole = new VConsole();
  // }
});
</script>

<style lang="scss">
/* 样式部分保持不变 */
page {
  font-family: PingFangSC, PingFang SC;
  font-size: 28rpx;
  color: #4d4d4d;
}

:deep(.uni-nav-bar-text) {
  font-size: 16px !important;
  font-weight: 600 !important;
}
</style>
