
import http from "@/utils/request"

// import useStore from '@/store'
// const { user } = useStore()

export function login(data) {
    return http.post('/login', data).then((response) => {
        if (response && response.data) {
            return response.data
        } else {
            console.log('登录失败', response)
            return Promise.reject(new Error("登录失败"))
        }
    })
        .catch((error) => {
            console.log(error)
            return Promise.reject(new Error(`Request Login failed: ${error}`))
        })
}

/**
 * 获取银行卡列表
 * @returns {Promise} 银行卡列表数据
 */
export function getBankCardList() {
    return http.get('/bankCard/list').then((response) => {
        if (response && response.data) {
            return response.data
        } else {
            console.log('获取银行卡列表失败', response)
            return Promise.reject(new Error("获取银行卡列表失败"))
        }
    })
        .catch((error) => {
            console.log(error)
            return Promise.reject(new Error(`获取银行卡列表失败: ${error}`))
        })
}

/**
 * 解绑银行卡
 * @param {string} cardId - 银行卡ID
 * @returns {Promise} 解绑结果
 */
export function unbindBankCard(cardId) {
    return http.post('/bankCard/unbind', { cardId }).then((response) => {
        if (response && response.data) {
            return response.data
        } else {
            console.log('解绑银行卡失败', response)
            return Promise.reject(new Error("解绑银行卡失败"))
        }
    })
        .catch((error) => {
            console.log(error)
            return Promise.reject(new Error(`解绑银行卡失败: ${error}`))
        })
}

