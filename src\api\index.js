
import http from "@/utils/request"

// import useStore from '@/store'
// const { user } = useStore()

export function login(data) {
    return http.post('/login', data).then((response) => {
        if (response && response.data) {
            return response.data
        } else {
            console.log('登录失败', response)
            return Promise.reject(new Error("登录失败"))
        }
    })
        .catch((error) => {
            console.log(error)
            return Promise.reject(new Error(`Request Login failed: ${error}`))
        })
}

