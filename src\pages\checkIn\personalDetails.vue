<template>
  <view class="page-container">
    <view class="form-container">
      <!-- 表单标题 -->
      <view class="form-title">个人基本信息</view>
      <!-- 表单内容 -->
      <view class="form-content">
        <!-- 姓名 -->
        <view class="form-item">
          <view class="label required">
            <text class="required-star">*</text>
            <text class="label-text">姓名</text>
          </view>
          <input
            class="form-input"
            v-model="formData.name"
            placeholder="请输入姓名"
            maxlength="20"
            @blur="validateField('name')"
          />
        </view>

        <!-- 性别 -->
        <view class="form-item">
          <view class="label required">
            <text class="required-star">*</text>
            <text class="label-text">性别</text>
          </view>
          <uni-data-select
            class="noBorder"
            :clear="false"
            v-model="formData.gender"
            :localdata="genderOptions"
            placeholder="请选择"
            @change="onGenderChange"
          ></uni-data-select>
        </view>

        <!-- 手机号 -->
        <view class="form-item">
          <view class="label required">
            <text class="required-star">*</text>
            <text class="label-text">手机号</text>
          </view>
          <input
            class="form-input"
            v-model="formData.phone"
            placeholder="请输入手机号"
            type="number"
            maxlength="11"
            @blur="validateField('phone')"
          />
        </view>

        <!-- 专业 -->
        <view class="form-item">
          <view class="label required">
            <text class="required-star">*</text>
            <text class="label-text">专业</text>
          </view>
          <input
            class="form-input"
            v-model="selectSpecialty.majorName"
            disabled
          />
        </view>

        <!-- 邮箱 -->
        <view class="form-item">
          <view class="label">
            <text class="required-star">*</text>
            <text class="label-text">邮箱</text>
          </view>
          <input
            class="form-input"
            v-model="formData.email"
            placeholder="请输入邮箱"
            type="email"
          />
        </view>

        <!-- 身份证号 -->
        <view class="form-item">
          <view class="label">
            <text class="required-star">*</text>
            <text class="label-text">身份证号</text>
          </view>
          <input
            class="form-input"
            v-model="formData.idCard"
            placeholder="请输入身份证号"
            maxlength="18"
            @blur="validateField('idCard')"
          />
        </view>

        <!-- 出生日期 -->
        <view class="form-item">
          <view class="label">
            <text class="required-star">*</text>
            <text class="label-text">出生日期</text>
          </view>
          <uni-datetime-picker
            v-model="formData.birthday"
            type="date"
            :border="false"
            placeholder="请选择出生日期"
            :clear-icon="false"
            @change="onBirthdayChange"
          ></uni-datetime-picker>
        </view>

        <!-- 政治面貌 -->
        <view class="form-item">
          <view class="label">
            <text class="required-star">*</text>
            <text class="label-text">政治面貌</text>
          </view>
          <uni-data-select
            :clear="false"
            v-model="formData.politicalStatus"
            :localdata="politicalOptions"
            placeholder="请选择"
            @change="onPoliticalStatusChange"
          ></uni-data-select>
        </view>

        <!-- 民族 -->
        <view class="form-item">
          <view class="label">
            <text class="required-star">*</text>
            <text class="label-text">民族</text>
          </view>
          <uni-data-select
            :clear="false"
            v-model="formData.nation"
            :localdata="ethnicityOptions"
            placeholder="请选择"
            @change="onNationChange"
          ></uni-data-select>
        </view>

        <!-- 籍贯 -->
        <view class="form-item">
          <view class="label">
            <text class="required-star">*</text>
            <text class="label-text">籍贯</text>
          </view>
          <uni-data-picker
            :clear-icon="false"
            :map="{ text: 'name', value: 'id' }"
            v-model="formData.nativePlace"
            :localdata="provinceOptions"
            popup-title="请选择籍贯"
            @change="onNativePlacechange"
          ></uni-data-picker>
        </view>

        <!-- 户籍所在地 -->
        <view class="form-item">
          <view class="label">
            <text class="required-star">*</text>
            <text class="label-text">户籍所在地</text>
          </view>
          <input
            class="form-input"
            v-model="formData.householdAddress"
            placeholder="请输入户籍所在地"
            @blur="validateField('householdAddress')"
          />
        </view>
        <!-- 毕业院校 -->
        <view class="form-item">
          <view class="label">
            <text class="required-star">*</text>
            <text class="label-text">毕业院校</text>
          </view>
          <input
            class="form-input"
            v-model="formData.graduationSchool"
            placeholder="请输入毕业院校"
            maxlength="20"
            @blur="validateField('graduationSchool')"
          />
        </view>

        <!-- 是否外地学籍 -->
        <view class="form-item">
          <view class="label required">
            <text class="required-star">*</text>
            <text class="label-text">是否外地学籍</text>
          </view>
          <uni-data-select
            :clear="false"
            placement="top"
            class="noBorder"
            v-model="formData.isNonlocalStudent"
            :localdata="isNonlocalStudentOpt"
            placeholder="请选择"
            @change="isNonlocalStudentChange"
          ></uni-data-select>
        </view>

        <!-- 家庭现住址 -->
        <view class="form-item">
          <view class="label">
            <text class="required-star">*</text>
            <text class="label-text">家庭现住址</text>
          </view>
          <input
            class="form-input"
            v-model="formData.address"
            placeholder="请输入家庭现住址"
            @blur="validateField('address')"
          />
        </view>

        <!-- 考试总分 -->
        <view class="form-item">
          <view class="label">
            <text class="required-star">*</text>
            <text class="label-text">考试总分</text>
          </view>
          <input
            class="form-input"
            v-model="formData.totalScore"
            placeholder="请输入考试总分"
            type="number"
            @blur="validateField('totalScore')"
          />
        </view>

        <!-- 是否住校 -->
        <view class="form-item">
          <view class="label required">
            <text class="required-star">*</text>
            <text class="label-text">是否住校</text>
          </view>
          <uni-data-select
            :clear="false"
            placement="top"
            class="noBorder"
            v-model="formData.isDormitory"
            :localdata="isDormitoryOpt"
            placeholder="请选择"
            @change="onIsDormitoryChange"
          ></uni-data-select>
        </view>

        <!-- 宿舍类型  -->
        <view class="form-item" v-if="!!formData.isDormitory">
          <view class="label required">
            <text class="required-star">*</text>
            <text class="label-text">宿舍类型</text>
          </view>
          <uni-data-select
            :clear="false"
            placement="top"
            class="noBorder"
            v-model="formData.dormitoryId"
            :localdata="roomTypeOpt"
            placeholder="请选择"
            @change="onDormitoryIdChange"
          ></uni-data-select>
        </view>
      </view>
    </view>

    <!-- 固定底部按钮 -->
    <view class="bottom-fixed">
      <button
        class="submit-btn"
        :class="{ disabled: !canSubmit }"
        @click="handleSubmit"
      >
        下一步
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, reactive, onMounted } from "vue";
import http from "@/utils/request";
import { useSafeStorage } from "@/hooks/useSafeStorage";
import useCheckInStore from "@/store/checkIn";

import {
  provinceOptions,
  ethnicityOptions,
  politicalOptions,
  genderOptions,
  isDormitoryOpt,
  isNonlocalStudentOpt
} from "@/utils/china-area-data";

const roomTypeOpt = ref([]);
// 获取缓存中选中的专业对象
const selectSpecialty = useSafeStorage("checkIn-selectSpecialty", {});

// 使用 Pinia store 管理表单数据
const checkInStore = useCheckInStore();

// 表单数据 - 直接使用 Pinia store 中的数据
const formData = checkInStore.personalInfo;

// 表单验证错误
const formErrors = reactive({
  name: "",
  gender: "",
  phone: "",
  email: "",
  idCard: "",
  birthday: "",
  politicalStatus: "",
  nation: "",
  nativePlace: "",
  householdAddress: "",
  address: "",
  totalScore: "",
  isDormitory: "",
  dormitoryId: "",
});

const onIsDormitoryChange = (val) => {
  console.log(val, "是否住校");
  if (!val) {
    formData.dormitoryId = null;
  }
  // 清除相关错误信息
  formErrors.isDormitory = "";
  formErrors.dormitoryId = "";
};



// 是否外地学籍
const isNonlocalStudentChange  = (val) => {
  formErrors.isNonlocalStudent = "";
};

// 计算属性：是否可以提交
const canSubmit = computed(() => {
  // 基础必填项校验
  const basicRequired =
    formData.name !== "" &&
    formData.gender !== "" &&
    formData.phone !== "" &&
    formData.birthday !== "" &&
    formData.politicalStatus !== "" &&
    formData.nation !== "" &&
    formData.nativePlace !== "" &&
    formData.householdAddress !== "" &&
    formData.isNonlocalStudent !== "" &&
    formData.graduationSchool !== "" &&
    formData.address !== "" &&
    formData.totalScore !== "" &&
    formData.isDormitory !== "";

  console.log(basicRequired, "1212");

  console.log(formData, "1212");

  // 住校相关校验：如果选择住校，宿舍类型也必填
  const dormitoryRequired =
    formData.isDormitory === 1 ? formData.dormitoryId !== "" : true;

  // 所有错误信息都为空
  const noErrors = Object.values(formErrors).every((error) => error === "");

  return basicRequired && dormitoryRequired && noErrors;
});

// 验证方法
const validateField = (field) => {
  switch (field) {
    case "name":
      if (!formData.name.trim()) {
        formErrors.name = "请输入姓名";
      } else if (formData.name.length < 2) {
        formErrors.name = "姓名至少2个字符";
      } else {
        formErrors.name = "";
      }
      break;

    case "phone":
      const phoneReg = /^1[3-9]\d{9}$/;
      if (!formData.phone.trim()) {
        formErrors.phone = "请输入手机号";
      } else if (!phoneReg.test(formData.phone)) {
        formErrors.phone = "请输入正确的手机号";
      } else {
        formErrors.phone = "";
      }
      break;

    case "email":
      if (formData.email.trim()) {
        const emailReg = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailReg.test(formData.email)) {
          formErrors.email = "请输入正确的邮箱格式";
        } else {
          formErrors.email = "";
        }
      } else {
        formErrors.email = "";
      }
      break;

    case "idCard":
      if (formData.idCard.trim()) {
        const idCardReg =
          /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
        if (!idCardReg.test(formData.idCard)) {
          formErrors.idCard = "请输入正确的身份证号";
        } else {
          formErrors.idCard = "";
        }
      } else {
        formErrors.idCard = "";
      }
      break;

    case "birthday":
      if (!formData.birthday) {
        formErrors.birthday = "请选择出生日期";
      } else {
        formErrors.birthday = "";
      }
      break;

    case "politicalStatus":
      if (!formData.politicalStatus) {
        formErrors.politicalStatus = "请选择政治面貌";
      } else {
        formErrors.politicalStatus = "";
      }
      break;

    case "nation":
      if (!formData.nation) {
        formErrors.nation = "请选择民族";
      } else {
        formErrors.nation = "";
      }
      break;

    case "nativePlace":
      if (!formData.nativePlace || formData.nativePlace.length === 0) {
        formErrors.nativePlace = "请选择籍贯";
      } else {
        formErrors.nativePlace = "";
      }
      break;

    case "householdAddress":
      if (!formData.householdAddress) {
        formErrors.householdAddress = "请输入户籍所在地";
      } else {
        formErrors.householdAddress = "";
      }
      break;

      case "isNonlocalStudent":
      if (formData.isNonlocalStudent === "") {
        formErrors.isNonlocalStudent = "请选择是否外地学籍";
      } else {
        formErrors.isNonlocalStudent = "";
      }
      break;

    case "graduationSchool":
      if (!formData.graduationSchool) {
        formErrors.graduationSchool = "请输入毕业院校";
      } else {
        formErrors.graduationSchool = "";
      }
      break;

    case "address":
      if (!formData.address.trim()) {
        formErrors.address = "请输入家庭现住址";
      } else {
        formErrors.address = "";
      }
      break;

    case "totalScore":
      if (!formData.totalScore) {
        formErrors.totalScore = "请输入考试总分";
      } else if (
        isNaN(formData.totalScore) ||
        Number(formData.totalScore) < 0
      ) {
        formErrors.totalScore = "请输入有效的考试总分";
      } else {
        formErrors.totalScore = "";
      }
      break;

    case "isDormitory":
      if (formData.isDormitory === "") {
        formErrors.isDormitory = "请选择是否住校";
      } else {
        formErrors.isDormitory = "";
      }
      break;

    case "dormitoryId":
      // 只有在选择住校时才校验宿舍类型
      if (formData.isDormitory === 1 && !formData.dormitoryId) {
        formErrors.dormitoryId = "请选择宿舍类型";
      } else {
        formErrors.dormitoryId = "";
      }
      break;
  }

  // 显示错误提示
  if (formErrors[field]) {
    uni.showToast({
      title: formErrors[field],
      icon: "none",
      duration: 2000,
    });
  }
};

// 选择器变化事件
const onGenderChange = (e) => {
  formData.gender = e;
  formErrors.gender = "";
  // 数据会通过 Pinia 自动持久化
};

const onBirthdayChange = (e) => {
  formData.birthday = e;
  validateField("birthday");
  // 数据会通过 Pinia 自动持久化
};

const onPoliticalStatusChange = (e) => {
  formData.politicalStatus = e;
  validateField("politicalStatus");
  // 数据会通过 Pinia 自动持久化
};

const onNationChange = (e) => {
  formData.nation = e;
  validateField("nation");
  // 数据会通过 Pinia 自动持久化
};

const onNativePlacechange = (e) => {
  formData.nativePlaces = e.detail.value.map((item) => item.value);
  validateField("nativePlace");
  // 数据会通过 Pinia 自动持久化
};

const onDormitoryIdChange = (e) => {
  formData.dormitoryId = e;
  validateField("dormitoryId");
  // 数据会通过 Pinia 自动持久化
};

// 提交表单
const handleSubmit = () => {
  // 检查具体哪个必填项没有填写
  const checkRequiredFields = () => {
    // 基础必填项检查
    if (!formData.name.trim()) {
      return "请输入姓名";
    }
    if (formData.gender === "") {
      return "请选择性别";
    }
    if (!formData.phone.trim()) {
      return "请输入手机号";
    }
    if (!formData.birthday) {
      return "请选择出生日期";
    }
    if (!formData.politicalStatus) {
      return "请选择政治面貌";
    }
    if (!formData.nation) {
      return "请选择民族";
    }
    if (!formData.nativePlace || formData.nativePlace.length === 0) {
      return "请选择籍贯";
    }
    if (!formData.householdAddress.trim()) {
      return "请输入户籍所在地";
    }
    if (!formData.address.trim()) {
      return "请输入家庭现住址";
    }
    if (!formData.totalScore) {
      return "请输入考试总分";
    }
    if (formData.isDormitory === "") {
      return "请选择是否住校";
    }
    // 如果选择住校，检查宿舍类型
    if (formData.isDormitory === 1 && !formData.dormitoryId) {
      return "请选择宿舍类型";
    }

    return null; // 所有必填项都已填写
  };

  // 验证所有必填项并获取具体的错误信息
  validateField("name");
  validateField("phone");
  validateField("birthday");
  validateField("politicalStatus");
  validateField("nation");
  validateField("nativePlace");
  validateField("householdAddress");
  validateField("address");
  validateField("totalScore");
  validateField("isDormitory");

  // 如果选择住校，验证宿舍类型
  if (formData.isDormitory === 1) {
    validateField("dormitoryId");
  }

  // 检查是否有具体的必填项未填写
  const missingFieldMessage = checkRequiredFields();
  if (missingFieldMessage) {
    uni.showToast({
      title: missingFieldMessage,
      icon: "none",
    });
    return;
  }

  // 检查是否有验证错误
  const hasErrors = Object.values(formErrors).some((error) => error !== "");
  if (hasErrors) {
    // 找到第一个有错误的字段并显示
    const firstError = Object.values(formErrors).find((error) => error !== "");
    uni.showToast({
      title: firstError,
      icon: "none",
    });
    return;
  }

  // 数据已经通过 Pinia 自动保存，无需手动缓存
  console.log("个人信息已保存:", formData);

  // 到下一步去填写家庭成员
  uni.navigateTo({
    url: "/pages/checkIn/familyInfo",
  });
};

function getDormitory() {
  // 1,.专业子项 ；2.宿舍子项；3.其他子项
  http
    .post("/app/enrollment/payment-item/dormitoryList", {
      builtinType: 2,
      planId: uni.getStorageSync("planId") || null,
    })
    .then((res) => {
      roomTypeOpt.value = res.data.map((item) => {
        return {
          value: item.id,
          text: item.subName,
        };
      });
    });
}

onMounted(() => {
  getDormitory();
});
</script>

<style scoped>
.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

.form-container {
  /* margin: 30rpx; */
  background-color: #ffffff;
  /* border-radius: 20rpx; */
  /* overflow: hidden; */
  /* box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06); */
}

.form-title {
  background-color: #f6f6f6;
  padding: 24rpx 46rpx;
  font-weight: 600;
  font-size: 28rpx;
  color: #333333;
}

.form-content {
  padding: 0 40rpx 40rpx;
}

.form-item {
  display: flex;
  align-items: center;
  min-height: 100rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  width: 180rpx;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.label.required {
  position: relative;
}

.required-star {
  color: #fd4f45;
  font-size: 24rpx;
  margin-right: 4rpx;
}

.label-text {
  line-height: 34rpx;

  font-weight: 600;
  font-size: 24rpx;
  color: #333333;
}

.form-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333333;
  padding: 0 20rpx;
  background-color: transparent;
}

.form-input .uni-input-placeholder {
  font-weight: 400;
  font-size: 24rpx;
  color: #bfbfbf;
}

/* uni-ui 组件样式调整 */
:deep(.uni-select) {
  flex: 1;
  border: none !important;
}

:deep(.uni-select .uni-select__input-box) {
  height: 80rpx !important;
  border: none !important;
  background-color: transparent !important;
}

:deep(.uni-select .uni-select__input-text) {
  font-size: 28rpx !important ;
  color: #333333 !important;
}

:deep(.uni-select .uni-select__input-placeholder) {
  font-weight: 400 !important;
  font-size: 24rpx !important;
  color: #bfbfbf !important;
}

:deep(.uni-date__x-input) {
  /* font-weight: 400 !important;
  font-size: 24rpx !important;
  color: #bfbfbf !important; */
}

:deep(.uni-datetime-picker) {
  flex: 1;
}

:deep(.uni-datetime-picker .uni-datetime-picker-view) {
  height: 80rpx !important;
  border: none !important;
  background-color: transparent !important;
}

:deep(.uni-datetime-picker .uni-datetime-picker-text) {
  font-size: 28rpx !important;
  color: #333333 !important;
}

:deep(.uni-datetime-picker .uni-datetime-picker-placeholder) {
  color: #c0c0c0 !important;
  font-size: 28rpx !important;
}

/* 固定底部按钮 */
.bottom-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
}

.submit-btn {
  width: 100%;
  height: 80rpx;
  background: #00b781;
  color: #ffffff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.submit-btn.disabled {
  background: #cccccc;
  color: #999999;
}

:deep(.uni-date-single .uni-icons) {
  display: none;
}

:deep(.uni-date__x-input) {
  padding-left: 10px;
}

:deep(.selected-area.placeholder) {
  font-weight: 400;
  font-size: 24rpx;
  color: #bfbfbf;
}

:deep(.input-value-border) {
  border: none !important;
}
.noBorder {
  :deep(.uni-select) {
    border: none !important;
  }
}
</style>
